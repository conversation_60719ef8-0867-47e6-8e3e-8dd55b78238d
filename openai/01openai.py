import os

from openai import OpenAI


client = OpenAI(
  base_url="https://openrouter.ai/api/v1",
  api_key=os.getenv("DEEPSEEK_API_KEY"),
)

completion = client.chat.completions.create(
  model="deepseek/deepseek-r1-0528:free",
  messages=[
    {
      "role": "user",
      "content": "What is the meaning of life?, 使用中文回答",
    }
  ],
  stream=True
)

for chunk in completion:
    print(chunk.choices[0].delta.content, end="")
print("\n")